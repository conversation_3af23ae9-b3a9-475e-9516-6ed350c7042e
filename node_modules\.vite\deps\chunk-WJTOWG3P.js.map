{"version": 3, "sources": ["../../meshline/dist/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport * as THREE from \"three\";\nfunction memcpy(src, srcOffset, dst, dstOffset, length) {\n  let i;\n  src = src.subarray || src.slice ? src : src.buffer;\n  dst = dst.subarray || dst.slice ? dst : dst.buffer;\n  src = srcOffset ? src.subarray ? src.subarray(srcOffset, length && srcOffset + length) : src.slice(srcOffset, length && srcOffset + length) : src;\n  if (dst.set) {\n    dst.set(src, dstOffset);\n  } else {\n    for (i = 0; i < src.length; i++)\n      dst[i + dstOffset] = src[i];\n  }\n  return dst;\n}\nfunction convertPoints(points) {\n  if (points instanceof Float32Array)\n    return points;\n  if (points instanceof THREE.BufferGeometry)\n    return points.getAttribute(\"position\").array;\n  return points.map((p) => {\n    const isArray = Array.isArray(p);\n    return p instanceof THREE.Vector3 ? [p.x, p.y, p.z] : p instanceof THREE.Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n  }).flat();\n}\nclass MeshLineGeometry extends THREE.BufferGeometry {\n  constructor() {\n    super();\n    __publicField(this, \"type\", \"MeshLine\");\n    __publicField(this, \"isMeshLine\", true);\n    __publicField(this, \"positions\", []);\n    __publicField(this, \"previous\", []);\n    __publicField(this, \"next\", []);\n    __publicField(this, \"side\", []);\n    __publicField(this, \"width\", []);\n    __publicField(this, \"indices_array\", []);\n    __publicField(this, \"uvs\", []);\n    __publicField(this, \"counters\", []);\n    __publicField(this, \"widthCallback\", null);\n    __publicField(this, \"_attributes\");\n    __publicField(this, \"_points\", []);\n    __publicField(this, \"points\");\n    __publicField(this, \"matrixWorld\", new THREE.Matrix4());\n    Object.defineProperties(this, {\n      points: {\n        enumerable: true,\n        get() {\n          return this._points;\n        },\n        set(value) {\n          this.setPoints(value, this.widthCallback);\n        }\n      }\n    });\n  }\n  setMatrixWorld(matrixWorld) {\n    this.matrixWorld = matrixWorld;\n  }\n  setPoints(points, wcb) {\n    points = convertPoints(points);\n    this._points = points;\n    this.widthCallback = wcb != null ? wcb : null;\n    this.positions = [];\n    this.counters = [];\n    if (points.length && points[0] instanceof THREE.Vector3) {\n      for (let j = 0; j < points.length; j++) {\n        const p = points[j];\n        const c = j / (points.length - 1);\n        this.positions.push(p.x, p.y, p.z);\n        this.positions.push(p.x, p.y, p.z);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    } else {\n      for (let j = 0; j < points.length; j += 3) {\n        const c = j / (points.length - 1);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.positions.push(points[j], points[j + 1], points[j + 2]);\n        this.counters.push(c);\n        this.counters.push(c);\n      }\n    }\n    this.process();\n  }\n  compareV3(a, b) {\n    const aa = a * 6;\n    const ab = b * 6;\n    return this.positions[aa] === this.positions[ab] && this.positions[aa + 1] === this.positions[ab + 1] && this.positions[aa + 2] === this.positions[ab + 2];\n  }\n  copyV3(a) {\n    const aa = a * 6;\n    return [this.positions[aa], this.positions[aa + 1], this.positions[aa + 2]];\n  }\n  process() {\n    const l = this.positions.length / 6;\n    this.previous = [];\n    this.next = [];\n    this.side = [];\n    this.width = [];\n    this.indices_array = [];\n    this.uvs = [];\n    let w;\n    let v;\n    if (this.compareV3(0, l - 1)) {\n      v = this.copyV3(l - 2);\n    } else {\n      v = this.copyV3(0);\n    }\n    this.previous.push(v[0], v[1], v[2]);\n    this.previous.push(v[0], v[1], v[2]);\n    for (let j = 0; j < l; j++) {\n      this.side.push(1);\n      this.side.push(-1);\n      if (this.widthCallback)\n        w = this.widthCallback(j / (l - 1));\n      else\n        w = 1;\n      this.width.push(w);\n      this.width.push(w);\n      this.uvs.push(j / (l - 1), 0);\n      this.uvs.push(j / (l - 1), 1);\n      if (j < l - 1) {\n        v = this.copyV3(j);\n        this.previous.push(v[0], v[1], v[2]);\n        this.previous.push(v[0], v[1], v[2]);\n        const n = j * 2;\n        this.indices_array.push(n, n + 1, n + 2);\n        this.indices_array.push(n + 2, n + 1, n + 3);\n      }\n      if (j > 0) {\n        v = this.copyV3(j);\n        this.next.push(v[0], v[1], v[2]);\n        this.next.push(v[0], v[1], v[2]);\n      }\n    }\n    if (this.compareV3(l - 1, 0)) {\n      v = this.copyV3(1);\n    } else {\n      v = this.copyV3(l - 1);\n    }\n    this.next.push(v[0], v[1], v[2]);\n    this.next.push(v[0], v[1], v[2]);\n    if (!this._attributes || this._attributes.position.count !== this.counters.length) {\n      this._attributes = {\n        position: new THREE.BufferAttribute(new Float32Array(this.positions), 3),\n        previous: new THREE.BufferAttribute(new Float32Array(this.previous), 3),\n        next: new THREE.BufferAttribute(new Float32Array(this.next), 3),\n        side: new THREE.BufferAttribute(new Float32Array(this.side), 1),\n        width: new THREE.BufferAttribute(new Float32Array(this.width), 1),\n        uv: new THREE.BufferAttribute(new Float32Array(this.uvs), 2),\n        index: new THREE.BufferAttribute(new Uint16Array(this.indices_array), 1),\n        counters: new THREE.BufferAttribute(new Float32Array(this.counters), 1)\n      };\n    } else {\n      this._attributes.position.copyArray(new Float32Array(this.positions));\n      this._attributes.position.needsUpdate = true;\n      this._attributes.previous.copyArray(new Float32Array(this.previous));\n      this._attributes.previous.needsUpdate = true;\n      this._attributes.next.copyArray(new Float32Array(this.next));\n      this._attributes.next.needsUpdate = true;\n      this._attributes.side.copyArray(new Float32Array(this.side));\n      this._attributes.side.needsUpdate = true;\n      this._attributes.width.copyArray(new Float32Array(this.width));\n      this._attributes.width.needsUpdate = true;\n      this._attributes.uv.copyArray(new Float32Array(this.uvs));\n      this._attributes.uv.needsUpdate = true;\n      this._attributes.index.copyArray(new Uint16Array(this.indices_array));\n      this._attributes.index.needsUpdate = true;\n    }\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setAttribute(\"position\", this._attributes.position);\n    this.setAttribute(\"previous\", this._attributes.previous);\n    this.setAttribute(\"next\", this._attributes.next);\n    this.setAttribute(\"side\", this._attributes.side);\n    this.setAttribute(\"width\", this._attributes.width);\n    this.setAttribute(\"uv\", this._attributes.uv);\n    this.setAttribute(\"counters\", this._attributes.counters);\n    this.setIndex(this._attributes.index);\n    this.computeBoundingSphere();\n    this.computeBoundingBox();\n  }\n  advance({ x, y, z }) {\n    const positions = this._attributes.position.array;\n    const previous = this._attributes.previous.array;\n    const next = this._attributes.next.array;\n    const l = positions.length;\n    memcpy(positions, 0, previous, 0, l);\n    memcpy(positions, 6, positions, 0, l - 6);\n    positions[l - 6] = x;\n    positions[l - 5] = y;\n    positions[l - 4] = z;\n    positions[l - 3] = x;\n    positions[l - 2] = y;\n    positions[l - 1] = z;\n    memcpy(positions, 6, next, 0, l - 6);\n    next[l - 6] = x;\n    next[l - 5] = y;\n    next[l - 4] = z;\n    next[l - 3] = x;\n    next[l - 2] = y;\n    next[l - 1] = z;\n    this._attributes.position.needsUpdate = true;\n    this._attributes.previous.needsUpdate = true;\n    this._attributes.next.needsUpdate = true;\n  }\n}\nconst vertexShader = `\n  #include <common>\n  #include <logdepthbuf_pars_vertex>\n  #include <fog_pars_vertex>\n  #include <clipping_planes_pars_vertex>\n\n  attribute vec3 previous;\n  attribute vec3 next;\n  attribute float side;\n  attribute float width;\n  attribute float counters;\n  \n  uniform vec2 resolution;\n  uniform float lineWidth;\n  uniform vec3 color;\n  uniform float opacity;\n  uniform float sizeAttenuation;\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  vec2 fix(vec4 i, float aspect) {\n    vec2 res = i.xy / i.w;\n    res.x *= aspect;\n    return res;\n  }\n  \n  void main() {\n    float aspect = resolution.x / resolution.y;\n    vColor = vec4(color, opacity);\n    vUV = uv;\n    vCounters = counters;\n  \n    mat4 m = projectionMatrix * modelViewMatrix;\n    vec4 finalPosition = m * vec4(position, 1.0) * aspect;\n    vec4 prevPos = m * vec4(previous, 1.0);\n    vec4 nextPos = m * vec4(next, 1.0);\n  \n    vec2 currentP = fix(finalPosition, aspect);\n    vec2 prevP = fix(prevPos, aspect);\n    vec2 nextP = fix(nextPos, aspect);\n  \n    float w = lineWidth * width;\n  \n    vec2 dir;\n    if (nextP == currentP) dir = normalize(currentP - prevP);\n    else if (prevP == currentP) dir = normalize(nextP - currentP);\n    else {\n      vec2 dir1 = normalize(currentP - prevP);\n      vec2 dir2 = normalize(nextP - currentP);\n      dir = normalize(dir1 + dir2);\n  \n      vec2 perp = vec2(-dir1.y, dir1.x);\n      vec2 miter = vec2(-dir.y, dir.x);\n      //w = clamp(w / dot(miter, perp), 0., 4. * lineWidth * width);\n    }\n  \n    //vec2 normal = (cross(vec3(dir, 0.), vec3(0., 0., 1.))).xy;\n    vec4 normal = vec4(-dir.y, dir.x, 0., 1.);\n    normal.xy *= .5 * w;\n    //normal *= projectionMatrix;\n    if (sizeAttenuation == 0.) {\n      normal.xy *= finalPosition.w;\n      normal.xy /= (vec4(resolution, 0., 1.) * projectionMatrix).xy * aspect;\n    }\n  \n    finalPosition.xy += normal.xy * side;\n    gl_Position = finalPosition;\n    #include <logdepthbuf_vertex>\n    #include <fog_vertex>\n    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n    #include <clipping_planes_vertex>\n    #include <fog_vertex>\n  }\n`;\nconst version = /* @__PURE__ */ (() => parseInt(THREE.REVISION.replace(/\\D+/g, \"\")))();\nconst colorspace_fragment = version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\";\nconst fragmentShader = `\n  #include <fog_pars_fragment>\n  #include <logdepthbuf_pars_fragment>\n  #include <clipping_planes_pars_fragment>\n  \n  uniform sampler2D map;\n  uniform sampler2D alphaMap;\n  uniform float useGradient;\n  uniform float useMap;\n  uniform float useAlphaMap;\n  uniform float useDash;\n  uniform float dashArray;\n  uniform float dashOffset;\n  uniform float dashRatio;\n  uniform float visibility;\n  uniform float alphaTest;\n  uniform vec2 repeat;\n  uniform vec3 gradient[2];\n  \n  varying vec2 vUV;\n  varying vec4 vColor;\n  varying float vCounters;\n  \n  void main() {\n    #include <logdepthbuf_fragment>\n    vec4 diffuseColor = vColor;\n    if (useGradient == 1.) diffuseColor = vec4(mix(gradient[0], gradient[1], vCounters), 1.0);\n    if (useMap == 1.) diffuseColor *= texture2D(map, vUV * repeat);\n    if (useAlphaMap == 1.) diffuseColor.a *= texture2D(alphaMap, vUV * repeat).a;\n    if (diffuseColor.a < alphaTest) discard;\n    if (useDash == 1.) diffuseColor.a *= ceil(mod(vCounters + dashOffset, dashArray) - (dashArray * dashRatio));\n    diffuseColor.a *= step(vCounters, visibility);\n    #include <clipping_planes_fragment>\n    gl_FragColor = diffuseColor;     \n    #include <fog_fragment>\n    #include <tonemapping_fragment>\n    #include <${colorspace_fragment}>\n  }\n`;\nclass MeshLineMaterial extends THREE.ShaderMaterial {\n  constructor(parameters) {\n    super({\n      uniforms: {\n        ...THREE.UniformsLib.fog,\n        lineWidth: { value: 1 },\n        map: { value: null },\n        useMap: { value: 0 },\n        alphaMap: { value: null },\n        useAlphaMap: { value: 0 },\n        color: { value: new THREE.Color(16777215) },\n        gradient: { value: [new THREE.Color(16711680), new THREE.Color(65280)] },\n        opacity: { value: 1 },\n        resolution: { value: new THREE.Vector2(1, 1) },\n        sizeAttenuation: { value: 1 },\n        dashArray: { value: 0 },\n        dashOffset: { value: 0 },\n        dashRatio: { value: 0.5 },\n        useDash: { value: 0 },\n        useGradient: { value: 0 },\n        visibility: { value: 1 },\n        alphaTest: { value: 0 },\n        repeat: { value: new THREE.Vector2(1, 1) }\n      },\n      vertexShader,\n      fragmentShader\n    });\n    __publicField(this, \"lineWidth\");\n    __publicField(this, \"map\");\n    __publicField(this, \"useMap\");\n    __publicField(this, \"alphaMap\");\n    __publicField(this, \"useAlphaMap\");\n    __publicField(this, \"color\");\n    __publicField(this, \"gradient\");\n    __publicField(this, \"resolution\");\n    __publicField(this, \"sizeAttenuation\");\n    __publicField(this, \"dashArray\");\n    __publicField(this, \"dashOffset\");\n    __publicField(this, \"dashRatio\");\n    __publicField(this, \"useDash\");\n    __publicField(this, \"useGradient\");\n    __publicField(this, \"visibility\");\n    __publicField(this, \"repeat\");\n    this.type = \"MeshLineMaterial\";\n    Object.defineProperties(this, {\n      lineWidth: {\n        enumerable: true,\n        get() {\n          return this.uniforms.lineWidth.value;\n        },\n        set(value) {\n          this.uniforms.lineWidth.value = value;\n        }\n      },\n      map: {\n        enumerable: true,\n        get() {\n          return this.uniforms.map.value;\n        },\n        set(value) {\n          this.uniforms.map.value = value;\n        }\n      },\n      useMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useMap.value;\n        },\n        set(value) {\n          this.uniforms.useMap.value = value;\n        }\n      },\n      alphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaMap.value;\n        },\n        set(value) {\n          this.uniforms.alphaMap.value = value;\n        }\n      },\n      useAlphaMap: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useAlphaMap.value;\n        },\n        set(value) {\n          this.uniforms.useAlphaMap.value = value;\n        }\n      },\n      color: {\n        enumerable: true,\n        get() {\n          return this.uniforms.color.value;\n        },\n        set(value) {\n          this.uniforms.color.value = value;\n        }\n      },\n      gradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.gradient.value;\n        },\n        set(value) {\n          this.uniforms.gradient.value = value;\n        }\n      },\n      opacity: {\n        enumerable: true,\n        get() {\n          return this.uniforms.opacity.value;\n        },\n        set(value) {\n          this.uniforms.opacity.value = value;\n        }\n      },\n      resolution: {\n        enumerable: true,\n        get() {\n          return this.uniforms.resolution.value;\n        },\n        set(value) {\n          this.uniforms.resolution.value.copy(value);\n        }\n      },\n      sizeAttenuation: {\n        enumerable: true,\n        get() {\n          return this.uniforms.sizeAttenuation.value;\n        },\n        set(value) {\n          this.uniforms.sizeAttenuation.value = value;\n        }\n      },\n      dashArray: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashArray.value;\n        },\n        set(value) {\n          this.uniforms.dashArray.value = value;\n          this.useDash = value !== 0 ? 1 : 0;\n        }\n      },\n      dashOffset: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashOffset.value;\n        },\n        set(value) {\n          this.uniforms.dashOffset.value = value;\n        }\n      },\n      dashRatio: {\n        enumerable: true,\n        get() {\n          return this.uniforms.dashRatio.value;\n        },\n        set(value) {\n          this.uniforms.dashRatio.value = value;\n        }\n      },\n      useDash: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useDash.value;\n        },\n        set(value) {\n          this.uniforms.useDash.value = value;\n        }\n      },\n      useGradient: {\n        enumerable: true,\n        get() {\n          return this.uniforms.useGradient.value;\n        },\n        set(value) {\n          this.uniforms.useGradient.value = value;\n        }\n      },\n      visibility: {\n        enumerable: true,\n        get() {\n          return this.uniforms.visibility.value;\n        },\n        set(value) {\n          this.uniforms.visibility.value = value;\n        }\n      },\n      alphaTest: {\n        enumerable: true,\n        get() {\n          return this.uniforms.alphaTest.value;\n        },\n        set(value) {\n          this.uniforms.alphaTest.value = value;\n        }\n      },\n      repeat: {\n        enumerable: true,\n        get() {\n          return this.uniforms.repeat.value;\n        },\n        set(value) {\n          this.uniforms.repeat.value.copy(value);\n        }\n      }\n    });\n    this.setValues(parameters);\n  }\n  copy(source) {\n    super.copy(source);\n    this.lineWidth = source.lineWidth;\n    this.map = source.map;\n    this.useMap = source.useMap;\n    this.alphaMap = source.alphaMap;\n    this.useAlphaMap = source.useAlphaMap;\n    this.color.copy(source.color);\n    this.gradient = source.gradient;\n    this.opacity = source.opacity;\n    this.resolution.copy(source.resolution);\n    this.sizeAttenuation = source.sizeAttenuation;\n    this.dashArray = source.dashArray;\n    this.dashOffset = source.dashOffset;\n    this.dashRatio = source.dashRatio;\n    this.useDash = source.useDash;\n    this.useGradient = source.useGradient;\n    this.visibility = source.visibility;\n    this.alphaTest = source.alphaTest;\n    this.repeat.copy(source.repeat);\n    return this;\n  }\n}\nfunction raycast(raycaster, intersects) {\n  const inverseMatrix = new THREE.Matrix4();\n  const ray = new THREE.Ray();\n  const sphere = new THREE.Sphere();\n  const interRay = new THREE.Vector3();\n  const geometry = this.geometry;\n  sphere.copy(geometry.boundingSphere);\n  sphere.applyMatrix4(this.matrixWorld);\n  if (!raycaster.ray.intersectSphere(sphere, interRay))\n    return;\n  inverseMatrix.copy(this.matrixWorld).invert();\n  ray.copy(raycaster.ray).applyMatrix4(inverseMatrix);\n  const vStart = new THREE.Vector3();\n  const vEnd = new THREE.Vector3();\n  const interSegment = new THREE.Vector3();\n  const step = this instanceof THREE.LineSegments ? 2 : 1;\n  const index = geometry.index;\n  const attributes = geometry.attributes;\n  if (index !== null) {\n    const indices = index.array;\n    const positions = attributes.position.array;\n    const widths = attributes.width.array;\n    for (let i = 0, l = indices.length - 1; i < l; i += step) {\n      const a = indices[i];\n      const b = indices[i + 1];\n      vStart.fromArray(positions, a * 3);\n      vEnd.fromArray(positions, b * 3);\n      const width = widths[Math.floor(i / 3)] != void 0 ? widths[Math.floor(i / 3)] : 1;\n      const precision = raycaster.params.Line.threshold + this.material.lineWidth * width / 2;\n      const precisionSq = precision * precision;\n      const distSq = ray.distanceSqToSegment(vStart, vEnd, interRay, interSegment);\n      if (distSq > precisionSq)\n        continue;\n      interRay.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(interRay);\n      if (distance < raycaster.near || distance > raycaster.far)\n        continue;\n      intersects.push({\n        distance,\n        point: interSegment.clone().applyMatrix4(this.matrixWorld),\n        index: i,\n        face: null,\n        faceIndex: void 0,\n        object: this\n      });\n      i = l;\n    }\n  }\n}\nexport {\n  MeshLineGeometry,\n  MeshLineMaterial,\n  raycast\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,kBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,SAAO;AACT;AAEA,SAAS,OAAO,KAAK,WAAW,KAAK,WAAW,QAAQ;AACtD,MAAI;AACJ,QAAM,IAAI,YAAY,IAAI,QAAQ,MAAM,IAAI;AAC5C,QAAM,IAAI,YAAY,IAAI,QAAQ,MAAM,IAAI;AAC5C,QAAM,YAAY,IAAI,WAAW,IAAI,SAAS,WAAW,UAAU,YAAY,MAAM,IAAI,IAAI,MAAM,WAAW,UAAU,YAAY,MAAM,IAAI;AAC9I,MAAI,IAAI,KAAK;AACX,QAAI,IAAI,KAAK,SAAS;AAAA,EACxB,OAAO;AACL,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC1B,UAAI,IAAI,SAAS,IAAI,IAAI,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,kBAAkB;AACpB,WAAO;AACT,MAAI,kBAAwB;AAC1B,WAAO,OAAO,aAAa,UAAU,EAAE;AACzC,SAAO,OAAO,IAAI,CAAC,MAAM;AACvB,UAAM,UAAU,MAAM,QAAQ,CAAC;AAC/B,WAAO,aAAmB,UAAU,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,aAAmB,UAAU,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,WAAW,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,WAAW,EAAE,WAAW,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;AAAA,EACpM,CAAC,EAAE,KAAK;AACV;AACA,IAAM,mBAAN,cAAqC,eAAe;AAAA,EAClD,cAAc;AACZ,UAAM;AACN,kBAAc,MAAM,QAAQ,UAAU;AACtC,kBAAc,MAAM,cAAc,IAAI;AACtC,kBAAc,MAAM,aAAa,CAAC,CAAC;AACnC,kBAAc,MAAM,YAAY,CAAC,CAAC;AAClC,kBAAc,MAAM,QAAQ,CAAC,CAAC;AAC9B,kBAAc,MAAM,QAAQ,CAAC,CAAC;AAC9B,kBAAc,MAAM,SAAS,CAAC,CAAC;AAC/B,kBAAc,MAAM,iBAAiB,CAAC,CAAC;AACvC,kBAAc,MAAM,OAAO,CAAC,CAAC;AAC7B,kBAAc,MAAM,YAAY,CAAC,CAAC;AAClC,kBAAc,MAAM,iBAAiB,IAAI;AACzC,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,WAAW,CAAC,CAAC;AACjC,kBAAc,MAAM,QAAQ;AAC5B,kBAAc,MAAM,eAAe,IAAU,QAAQ,CAAC;AACtD,WAAO,iBAAiB,MAAM;AAAA,MAC5B,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,IAAI,OAAO;AACT,eAAK,UAAU,OAAO,KAAK,aAAa;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU,QAAQ,KAAK;AACrB,aAAS,cAAc,MAAM;AAC7B,SAAK,UAAU;AACf,SAAK,gBAAgB,OAAO,OAAO,MAAM;AACzC,SAAK,YAAY,CAAC;AAClB,SAAK,WAAW,CAAC;AACjB,QAAI,OAAO,UAAU,OAAO,CAAC,aAAmB,SAAS;AACvD,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,IAAI,OAAO,CAAC;AAClB,cAAM,IAAI,KAAK,OAAO,SAAS;AAC/B,aAAK,UAAU,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACjC,aAAK,UAAU,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACjC,aAAK,SAAS,KAAK,CAAC;AACpB,aAAK,SAAS,KAAK,CAAC;AAAA,MACtB;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,cAAM,IAAI,KAAK,OAAO,SAAS;AAC/B,aAAK,UAAU,KAAK,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAC3D,aAAK,UAAU,KAAK,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAC3D,aAAK,SAAS,KAAK,CAAC;AACpB,aAAK,SAAS,KAAK,CAAC;AAAA,MACtB;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU,GAAG,GAAG;AACd,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,IAAI;AACf,WAAO,KAAK,UAAU,EAAE,MAAM,KAAK,UAAU,EAAE,KAAK,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,UAAU,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,EAC3J;AAAA,EACA,OAAO,GAAG;AACR,UAAM,KAAK,IAAI;AACf,WAAO,CAAC,KAAK,UAAU,EAAE,GAAG,KAAK,UAAU,KAAK,CAAC,GAAG,KAAK,UAAU,KAAK,CAAC,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU;AACR,UAAM,IAAI,KAAK,UAAU,SAAS;AAClC,SAAK,WAAW,CAAC;AACjB,SAAK,OAAO,CAAC;AACb,SAAK,OAAO,CAAC;AACb,SAAK,QAAQ,CAAC;AACd,SAAK,gBAAgB,CAAC;AACtB,SAAK,MAAM,CAAC;AACZ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,UAAU,GAAG,IAAI,CAAC,GAAG;AAC5B,UAAI,KAAK,OAAO,IAAI,CAAC;AAAA,IACvB,OAAO;AACL,UAAI,KAAK,OAAO,CAAC;AAAA,IACnB;AACA,SAAK,SAAS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,SAAK,SAAS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,KAAK,KAAK,CAAC;AAChB,WAAK,KAAK,KAAK,EAAE;AACjB,UAAI,KAAK;AACP,YAAI,KAAK,cAAc,KAAK,IAAI,EAAE;AAAA;AAElC,YAAI;AACN,WAAK,MAAM,KAAK,CAAC;AACjB,WAAK,MAAM,KAAK,CAAC;AACjB,WAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC5B,WAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC5B,UAAI,IAAI,IAAI,GAAG;AACb,YAAI,KAAK,OAAO,CAAC;AACjB,aAAK,SAAS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,aAAK,SAAS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,cAAM,IAAI,IAAI;AACd,aAAK,cAAc,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;AACvC,aAAK,cAAc,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,MAC7C;AACA,UAAI,IAAI,GAAG;AACT,YAAI,KAAK,OAAO,CAAC;AACjB,aAAK,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/B,aAAK,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MACjC;AAAA,IACF;AACA,QAAI,KAAK,UAAU,IAAI,GAAG,CAAC,GAAG;AAC5B,UAAI,KAAK,OAAO,CAAC;AAAA,IACnB,OAAO;AACL,UAAI,KAAK,OAAO,IAAI,CAAC;AAAA,IACvB;AACA,SAAK,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/B,SAAK,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/B,QAAI,CAAC,KAAK,eAAe,KAAK,YAAY,SAAS,UAAU,KAAK,SAAS,QAAQ;AACjF,WAAK,cAAc;AAAA,QACjB,UAAU,IAAU,gBAAgB,IAAI,aAAa,KAAK,SAAS,GAAG,CAAC;AAAA,QACvE,UAAU,IAAU,gBAAgB,IAAI,aAAa,KAAK,QAAQ,GAAG,CAAC;AAAA,QACtE,MAAM,IAAU,gBAAgB,IAAI,aAAa,KAAK,IAAI,GAAG,CAAC;AAAA,QAC9D,MAAM,IAAU,gBAAgB,IAAI,aAAa,KAAK,IAAI,GAAG,CAAC;AAAA,QAC9D,OAAO,IAAU,gBAAgB,IAAI,aAAa,KAAK,KAAK,GAAG,CAAC;AAAA,QAChE,IAAI,IAAU,gBAAgB,IAAI,aAAa,KAAK,GAAG,GAAG,CAAC;AAAA,QAC3D,OAAO,IAAU,gBAAgB,IAAI,YAAY,KAAK,aAAa,GAAG,CAAC;AAAA,QACvE,UAAU,IAAU,gBAAgB,IAAI,aAAa,KAAK,QAAQ,GAAG,CAAC;AAAA,MACxE;AAAA,IACF,OAAO;AACL,WAAK,YAAY,SAAS,UAAU,IAAI,aAAa,KAAK,SAAS,CAAC;AACpE,WAAK,YAAY,SAAS,cAAc;AACxC,WAAK,YAAY,SAAS,UAAU,IAAI,aAAa,KAAK,QAAQ,CAAC;AACnE,WAAK,YAAY,SAAS,cAAc;AACxC,WAAK,YAAY,KAAK,UAAU,IAAI,aAAa,KAAK,IAAI,CAAC;AAC3D,WAAK,YAAY,KAAK,cAAc;AACpC,WAAK,YAAY,KAAK,UAAU,IAAI,aAAa,KAAK,IAAI,CAAC;AAC3D,WAAK,YAAY,KAAK,cAAc;AACpC,WAAK,YAAY,MAAM,UAAU,IAAI,aAAa,KAAK,KAAK,CAAC;AAC7D,WAAK,YAAY,MAAM,cAAc;AACrC,WAAK,YAAY,GAAG,UAAU,IAAI,aAAa,KAAK,GAAG,CAAC;AACxD,WAAK,YAAY,GAAG,cAAc;AAClC,WAAK,YAAY,MAAM,UAAU,IAAI,YAAY,KAAK,aAAa,CAAC;AACpE,WAAK,YAAY,MAAM,cAAc;AAAA,IACvC;AACA,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI;AAC/C,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI;AAC/C,SAAK,aAAa,SAAS,KAAK,YAAY,KAAK;AACjD,SAAK,aAAa,MAAM,KAAK,YAAY,EAAE;AAC3C,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI;AAC/C,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI;AAC/C,SAAK,aAAa,SAAS,KAAK,YAAY,KAAK;AACjD,SAAK,aAAa,MAAM,KAAK,YAAY,EAAE;AAC3C,SAAK,aAAa,YAAY,KAAK,YAAY,QAAQ;AACvD,SAAK,SAAS,KAAK,YAAY,KAAK;AACpC,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG;AACnB,UAAM,YAAY,KAAK,YAAY,SAAS;AAC5C,UAAM,WAAW,KAAK,YAAY,SAAS;AAC3C,UAAM,OAAO,KAAK,YAAY,KAAK;AACnC,UAAM,IAAI,UAAU;AACpB,WAAO,WAAW,GAAG,UAAU,GAAG,CAAC;AACnC,WAAO,WAAW,GAAG,WAAW,GAAG,IAAI,CAAC;AACxC,cAAU,IAAI,CAAC,IAAI;AACnB,cAAU,IAAI,CAAC,IAAI;AACnB,cAAU,IAAI,CAAC,IAAI;AACnB,cAAU,IAAI,CAAC,IAAI;AACnB,cAAU,IAAI,CAAC,IAAI;AACnB,cAAU,IAAI,CAAC,IAAI;AACnB,WAAO,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;AACnC,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,IAAI,CAAC,IAAI;AACd,SAAK,YAAY,SAAS,cAAc;AACxC,SAAK,YAAY,SAAS,cAAc;AACxC,SAAK,YAAY,KAAK,cAAc;AAAA,EACtC;AACF;AACA,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4ErB,IAAM,WAA2B,MAAM,SAAe,SAAS,QAAQ,QAAQ,EAAE,CAAC,GAAG;AACrF,IAAM,sBAAsB,WAAW,MAAM,wBAAwB;AACrE,IAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAoCP,mBAAmB;AAAA;AAAA;AAGnC,IAAM,mBAAN,cAAqC,eAAe;AAAA,EAClD,YAAY,YAAY;AACtB,UAAM;AAAA,MACJ,UAAU;AAAA,QACR,GAAS,YAAY;AAAA,QACrB,WAAW,EAAE,OAAO,EAAE;AAAA,QACtB,KAAK,EAAE,OAAO,KAAK;AAAA,QACnB,QAAQ,EAAE,OAAO,EAAE;AAAA,QACnB,UAAU,EAAE,OAAO,KAAK;AAAA,QACxB,aAAa,EAAE,OAAO,EAAE;AAAA,QACxB,OAAO,EAAE,OAAO,IAAU,MAAM,QAAQ,EAAE;AAAA,QAC1C,UAAU,EAAE,OAAO,CAAC,IAAU,MAAM,QAAQ,GAAG,IAAU,MAAM,KAAK,CAAC,EAAE;AAAA,QACvE,SAAS,EAAE,OAAO,EAAE;AAAA,QACpB,YAAY,EAAE,OAAO,IAAU,QAAQ,GAAG,CAAC,EAAE;AAAA,QAC7C,iBAAiB,EAAE,OAAO,EAAE;AAAA,QAC5B,WAAW,EAAE,OAAO,EAAE;AAAA,QACtB,YAAY,EAAE,OAAO,EAAE;AAAA,QACvB,WAAW,EAAE,OAAO,IAAI;AAAA,QACxB,SAAS,EAAE,OAAO,EAAE;AAAA,QACpB,aAAa,EAAE,OAAO,EAAE;AAAA,QACxB,YAAY,EAAE,OAAO,EAAE;AAAA,QACvB,WAAW,EAAE,OAAO,EAAE;AAAA,QACtB,QAAQ,EAAE,OAAO,IAAU,QAAQ,GAAG,CAAC,EAAE;AAAA,MAC3C;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,KAAK;AACzB,kBAAc,MAAM,QAAQ;AAC5B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,iBAAiB;AACrC,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,QAAQ;AAC5B,SAAK,OAAO;AACZ,WAAO,iBAAiB,MAAM;AAAA,MAC5B,WAAW;AAAA,QACT,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,UAAU;AAAA,QACjC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,UAAU,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,IAAI;AAAA,QAC3B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,OAAO;AAAA,QAC9B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,OAAO,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,SAAS;AAAA,QAChC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,SAAS,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,YAAY;AAAA,QACnC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,YAAY,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,MAAM;AAAA,QAC7B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,MAAM,QAAQ;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,SAAS;AAAA,QAChC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,SAAS,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC/B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,QAAQ,QAAQ;AAAA,QAChC;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,WAAW;AAAA,QAClC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,WAAW,MAAM,KAAK,KAAK;AAAA,QAC3C;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,gBAAgB;AAAA,QACvC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,gBAAgB,QAAQ;AAAA,QACxC;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,UAAU;AAAA,QACjC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,UAAU,QAAQ;AAChC,eAAK,UAAU,UAAU,IAAI,IAAI;AAAA,QACnC;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,WAAW;AAAA,QAClC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,WAAW,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,UAAU;AAAA,QACjC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,UAAU,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC/B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,QAAQ,QAAQ;AAAA,QAChC;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,YAAY;AAAA,QACnC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,YAAY,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,WAAW;AAAA,QAClC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,WAAW,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,UAAU;AAAA,QACjC;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,UAAU,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,MAAM;AACJ,iBAAO,KAAK,SAAS,OAAO;AAAA,QAC9B;AAAA,QACA,IAAI,OAAO;AACT,eAAK,SAAS,OAAO,MAAM,KAAK,KAAK;AAAA,QACvC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,UAAU,UAAU;AAAA,EAC3B;AAAA,EACA,KAAK,QAAQ;AACX,UAAM,KAAK,MAAM;AACjB,SAAK,YAAY,OAAO;AACxB,SAAK,MAAM,OAAO;AAClB,SAAK,SAAS,OAAO;AACrB,SAAK,WAAW,OAAO;AACvB,SAAK,cAAc,OAAO;AAC1B,SAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,SAAK,WAAW,OAAO;AACvB,SAAK,UAAU,OAAO;AACtB,SAAK,WAAW,KAAK,OAAO,UAAU;AACtC,SAAK,kBAAkB,OAAO;AAC9B,SAAK,YAAY,OAAO;AACxB,SAAK,aAAa,OAAO;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,UAAU,OAAO;AACtB,SAAK,cAAc,OAAO;AAC1B,SAAK,aAAa,OAAO;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,OAAO,KAAK,OAAO,MAAM;AAC9B,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,WAAW,YAAY;AACtC,QAAM,gBAAgB,IAAU,QAAQ;AACxC,QAAM,MAAM,IAAU,IAAI;AAC1B,QAAM,SAAS,IAAU,OAAO;AAChC,QAAM,WAAW,IAAU,QAAQ;AACnC,QAAM,WAAW,KAAK;AACtB,SAAO,KAAK,SAAS,cAAc;AACnC,SAAO,aAAa,KAAK,WAAW;AACpC,MAAI,CAAC,UAAU,IAAI,gBAAgB,QAAQ,QAAQ;AACjD;AACF,gBAAc,KAAK,KAAK,WAAW,EAAE,OAAO;AAC5C,MAAI,KAAK,UAAU,GAAG,EAAE,aAAa,aAAa;AAClD,QAAM,SAAS,IAAU,QAAQ;AACjC,QAAM,OAAO,IAAU,QAAQ;AAC/B,QAAM,eAAe,IAAU,QAAQ;AACvC,QAAM,OAAO,gBAAsB,eAAe,IAAI;AACtD,QAAM,QAAQ,SAAS;AACvB,QAAM,aAAa,SAAS;AAC5B,MAAI,UAAU,MAAM;AAClB,UAAM,UAAU,MAAM;AACtB,UAAM,YAAY,WAAW,SAAS;AACtC,UAAM,SAAS,WAAW,MAAM;AAChC,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,IAAI,GAAG,KAAK,MAAM;AACxD,YAAM,IAAI,QAAQ,CAAC;AACnB,YAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,aAAO,UAAU,WAAW,IAAI,CAAC;AACjC,WAAK,UAAU,WAAW,IAAI,CAAC;AAC/B,YAAM,QAAQ,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC,KAAK,SAAS,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI;AAChF,YAAM,YAAY,UAAU,OAAO,KAAK,YAAY,KAAK,SAAS,YAAY,QAAQ;AACtF,YAAM,cAAc,YAAY;AAChC,YAAM,SAAS,IAAI,oBAAoB,QAAQ,MAAM,UAAU,YAAY;AAC3E,UAAI,SAAS;AACX;AACF,eAAS,aAAa,KAAK,WAAW;AACtC,YAAM,WAAW,UAAU,IAAI,OAAO,WAAW,QAAQ;AACzD,UAAI,WAAW,UAAU,QAAQ,WAAW,UAAU;AACpD;AACF,iBAAW,KAAK;AAAA,QACd;AAAA,QACA,OAAO,aAAa,MAAM,EAAE,aAAa,KAAK,WAAW;AAAA,QACzD,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACV,CAAC;AACD,UAAI;AAAA,IACN;AAAA,EACF;AACF;", "names": []}