import { Project, Experience, Education, ContactInfo } from '../types/terminal';

export const personalInfo = {
  name: "<PERSON><PERSON><PERSON>",
  title: "Full Stack Developer",
  bio: "Passionate developer with expertise in modern web technologies. I love creating efficient, scalable solutions and exploring new technologies.",
  location: "Your Location",
  yearsOfExperience: 3
};

export const skills = {
  languages: ["JavaScript", "TypeScript", "Python", "Rust", "Go", "Java"],
  frontend: ["React", "Vue.js", "Angular", "Next.js", "Svelte"],
  backend: ["Node.js", "Express", "FastAPI", "Django", "Spring Boot"],
  databases: ["PostgreSQL", "MongoDB", "Redis", "MySQL"],
  tools: ["Docker", "Kubernetes", "AWS", "Git", "Linux", "Vim"],
  other: ["GraphQL", "REST APIs", "Microservices", "CI/CD", "Testing"]
};

export const projects: Project[] = [
  {
    id: "1",
    name: "Terminal Portfolio",
    description: "Interactive terminal-style portfolio website built with React and TypeScript",
    technologies: ["React", "TypeScript", "Styled-Components", "Framer Motion"],
    githubUrl: "https://github.com/yourusername/terminal-portfolio",
    liveUrl: "https://yourportfolio.com",
    featured: true
  },
  {
    id: "2",
    name: "E-Commerce Platform",
    description: "Full-stack e-commerce solution with modern payment integration",
    technologies: ["Next.js", "Node.js", "PostgreSQL", "Stripe", "Docker"],
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    liveUrl: "https://yourecommerce.com",
    featured: true
  },
  {
    id: "3",
    name: "Task Management API",
    description: "RESTful API for task management with authentication and real-time updates",
    technologies: ["Rust", "Actix-web", "PostgreSQL", "WebSockets"],
    githubUrl: "https://github.com/yourusername/task-api",
    featured: false
  }
];

export const experience: Experience[] = [
  {
    id: "1",
    company: "Tech Company Inc.",
    position: "Senior Full Stack Developer",
    duration: "2022 - Present",
    description: "Led development of microservices architecture, mentored junior developers, and improved system performance by 40%",
    technologies: ["React", "Node.js", "AWS", "Docker", "PostgreSQL"]
  },
  {
    id: "2",
    company: "Startup Solutions",
    position: "Full Stack Developer",
    duration: "2020 - 2022",
    description: "Developed and maintained web applications, implemented CI/CD pipelines, and collaborated with cross-functional teams",
    technologies: ["Vue.js", "Python", "Django", "MySQL", "Redis"]
  }
];

export const education: Education[] = [
  {
    id: "1",
    institution: "University of Technology",
    degree: "Bachelor of Science in Computer Science",
    duration: "2016 - 2020",
    description: "Graduated with honors, specialized in software engineering and algorithms"
  }
];

export const contactInfo: ContactInfo = {
  email: "<EMAIL>",
  phone: "+****************",
  location: "Your City, Country",
  linkedin: "https://linkedin.com/in/yourprofile",
  github: "https://github.com/yourusername",
  website: "https://yourwebsite.com"
};
